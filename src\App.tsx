import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'

function App() {
  // 1.插值语句 jsx jsx{} 字符串 数字 数组(普通类型) 不能对象数组 但是可以数组数组  元素 三元表达式 api调用
  // 2.插值语句支持对象怎么用  需要序列化[{name:1}] {name:1}
  // 3.事件如何添加 驼峰 onClick 如需要传参 则用一个高阶匿名函数()=>func A 不需要则直接给函数体
  // 4.我想支持泛型函数 <T>会理解成一个泛型 如div  <T,>这样则可以实现
  // 5.如何去绑定 id class {}即可 id={xxxID} class比较特殊 必须用className={xxxClass}
  // 6.如何绑定多个class  使用模板插值语法``  className={`${classA} aaa bbb ccc` } 或者
  // 7.如何绑定style  {color:red; fontSize: '20px'}  注意style的值必须是一个对象
  // 8.添加html 代码片段 类似v-html dangerouslySetInnerHTML属性
  // 如何遍历数组
  const arr = [123, 123, 213, 14, 14, 1]

  const a = { name: 1 }
  const num = 12313.1313131
  const fn = (params) => {
    console.log('🚀 ~ params', params)
    console.log('点击事件触发了')
  }
  const fnA = <T,>(params: T) => {
    console.log('🚀 ~ params', params)
    console.log('🚀 ~ typeof params', typeof params)
    console.log('点击事件触发了')
  }
  const id = '123123123123'
  const classA = 'classA classB classC'
  const style1 = { color: 'red' }
  const dangerouslySetInnerHTML = `<h1>我是h1</h1>`
  return (
    <div>
      <p>
        原价：<del>¥100</del>
        现价：<ins>¥80</ins>
      </p>
      <div>{'smartree'}</div>
      <div>{12421941641}</div>
      <div>{[123, 124, 4, 1]}</div>
      <div>
        {[
          [123, 133, 1],
          [123, 12, 14],
        ]}
      </div>
      {/* <div>{[{ a: 123, b: 12, c: 14 }, { a: 123, b: 12, c: 14 }, [123, 12, 14]]}</div> */}
      <div>{<h4>哈哈哈</h4>}</div>
      <div>
        api调用,num{num}取两位小数{num.toFixed(2)}
      </div>
      <hr />
      <div>
        对象需序列化 方可使用 JSON.stringify()=={`>`} a:{JSON.stringify(a)}
      </div>
      <div onClick={() => fn(123)}>事件如何添加 驼峰onClick </div>
      <div onClick={() => fnA(123)}>我想支持泛型函数 </div>
      <div id={id} className={classA}>
        如何去绑定 id class {}使用花括号即可 id={'xxxID'} class比较特殊 必须用className{' '}
      </div>
      <div id={id} className={`${classA} aaa bbb ccc`}>
        如何绑定多个class 使用模板插值语法`${}`
      </div>
      <div style={style1}>如何绑定style style必须是一个键值对 {'color: red'}</div>
      <div> 添加html 代码片段 类似v-html dangerouslySetInnerHTML属性 当用到的标签里不能写文字</div>
      <div dangerouslySetInnerHTML={{ __html: dangerouslySetInnerHTML }}></div>
      <hr />
      <div>如何遍历数组</div>
      <div>
        {arr.map((item, index) => {
          return (
            <div id={item} key={index}>
              {index} - {item}
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default App
